import { HTTPException } from 'hono/http-exception'

import { eq } from 'drizzle-orm'
import { Hono } from 'hono'
import { fetcher } from 'itty-fetcher'

import MainLayout from '../../views/MainLayout'
import DetailPage from '../../views/VideoDetail'
import * as schema from '../db/schema'
import { cgeo, drizzle } from '../middlewares'
import { PlayableLinkService } from '../services/playable-link'
import {
  CONSTANTS,
  createVideoFormData,
  decodeHtmlEntities,
  ErrorSeverity,
  fetchAudioData,
  fetchVideoData,
  getIndexByTime,
  handleHttpError,
  headers,
  message,
  replaceRelativeHref,
  validateVideoId,
  videoConverterUrl,
  webBaseUrl,
} from '../utils'

const router = new Hono<AppEnv>()

router.get('/video/:id', cgeo(), drizzle(), MainLayout('S60Tube - Video Detail'), async (c) => {
  const videoId = c.req.param('id')
  if (!validateVideoId(videoId)) {
    throw new HTTPException(400, { message: message.INVALID_VIDEO_ID })
  }

  try {
    const { data: videoData } = await fetchVideoData({
      drizzle: c.get('drizzle'),
      videoId,
      cgeo: c.get('cgeo'),
      executionCtx: c.executionCtx,
    })

    const format = videoData.data.formats.find((f) => f.itag === CONSTANTS.VIDEO_ITAG)

    if (!format?.url) {
      throw new HTTPException(404, { message: 'Video format not found' })
    }

    c.executionCtx.waitUntil(
      PlayableLinkService.upsertPlayableLink(c.get('drizzle'), videoId, format.url, 'video'),
    )

    if (c.get('cgeo') == 'VN') {
      await fetcher().post(
        'https://duyph.io.vn/kv',
        {
          key: videoId,
          value: format.url,
        },
        {
          headers: {
            'content-type': 'application/json',
          },
        },
      )
    }

    return c.render(
      <DetailPage
        convertUrl={`/convert/${videoId}`}
        importUrl={`/import/${videoId}`}
        url={
          c.get('cgeo') == 'VN'
            ? `http://duyph.io.vn/videoplayback?v=${videoId}`
            : `${webBaseUrl}/videoplayback?v=${videoId}`
        }
        format={{
          mime_type: 'mp4',
          quality_label: '360p',
        }}
      />,
    )
  } catch {
    throw new HTTPException(500, { message: 'Please retry.' })
  }
})

router.get('/resolve', drizzle(), MainLayout('S60Tube - Video Detail'), async (c) => {
  const videoId = c.req.query('v')
  const convert = c.req.query('convert')
  const type = c.req.query('type')
  if (!videoId) throw new HTTPException(400, { message: 'Missing video ID' })

  if (
    c.req
      .header('User-Agent')
      ?.includes('Mozilla/5.0 (Windows NT 6.3; WOW64; rv:49.0) Gecko/20100101 Firefox/49.0')
  ) {
    return c.redirect(`/videoplayback?v=${videoId}`)
  }

  if (convert && (convert === 'true' || convert === '1')) {
    if (type === 'manual') {
      return c.redirect(`/import/${videoId}`)
    } else {
      return c.redirect(`/convert/${videoId}?type=${type || 'mp4'}`)
    }
  }

  try {
    const format = await PlayableLinkService.getPlayableLink(c.get('drizzle'), videoId, 'video')

    if (!format?.playableLink) {
      throw new HTTPException(404, { message: 'Video format not found' })
    }

    return c.render(
      <DetailPage
        convertUrl={`/convert/${videoId}`}
        importUrl={`/import/${videoId}`}
        url={`${webBaseUrl}/videoplayback?v=${videoId}`}
        format={{
          mime_type: 'mp4',
          quality_label: '360p',
        }}
      />,
    )
  } catch {
    throw new HTTPException(500, { message: 'Please retry.' })
  }
})

router.get('/convert/:id', drizzle(), async (c) => {
  const videoId = c.req.param('id')
  if (!validateVideoId(videoId)) {
    throw new HTTPException(400, { message: message.INVALID_VIDEO_ID })
  }
  const type = c.req.query('type') ?? 'mp4'
  const retry = c.req.query('retry')

  const formData = createVideoFormData(videoId, webBaseUrl)

  const accountIndex = getIndexByTime()
  const twoxyaAccount = await c
    .get('drizzle')
    .select({
      cookies: schema.twoxyaAccounts.cookies,
    })
    .from(schema.twoxyaAccounts)
    .where(eq(schema.twoxyaAccounts.id, Number(accountIndex)))
    .get()

  const resImport = await fetch(`${videoConverterUrl}/import.php`, {
    method: 'POST',
    body: formData,
    headers: headers(twoxyaAccount?.cookies ?? ''),
  })

  const htmlImport = await resImport.text()

  const [, id] = htmlImport.match(/<input[^>]*name=["']id["'][^>]*value=["'](\d+)["']/i) ?? []
  const [, ilove2yxa] =
    htmlImport.match(/<input[^>]*name=["']ILOVE2YXA["'][^>]*value=["'](.+)["']/i) ?? []

  if (!id) {
    if (!retry) {
      return c.redirect(`/convert/${videoId}?type=${type}&retry=true`)
    } else {
      throw new HTTPException(500, {
        message: 'Error converting video. Please retry.',
      })
    }
  }

  let bodyParams: Record<string, string> = {
    id,
    ext: type.split('-')[0],
    precet: '0',
    ILOVE2YXA: ilove2yxa,
    mode: 'makehandvideo',
    doconv: 'convert!',
    volume: '100',
    stereo: '1',
    ot: '00:00',
    do: '',
    vcodec: 'auto',
    maschtab: '1',
    kadr: '0',
  }

  if (type === 'mp3') {
    bodyParams = {
      zvuk: 'mp3',
      ab: '64',
      stereo: '1',
      volume: '100',
      ot: '00:00',
      do: '',
      gab1: 'none',
      id,
      ext: 'none',
      precet: bodyParams.precet,
      ILOVE2YXA: ilove2yxa,
      mode: bodyParams.mode,
      doconv: bodyParams.doconv,
    }
  } else if (type === '3gp-low') {
    bodyParams.vb = '128'

    bodyParams.zvuk = 'amr'
    bodyParams.ab = '12.20'

    bodyParams.gab1 = '352'
    bodyParams.gab2 = '288'
  } else {
    bodyParams.vb = '250'

    bodyParams.zvuk = 'aac'
    bodyParams.ab = '96'

    if (type === '3gp') {
      bodyParams.gab1 = '352'
      bodyParams.gab2 = '288'
    } else if (type === 'mp4') {
      bodyParams.gab1 = '320'
      bodyParams.gab2 = '180'
    }
  }

  const resMakeHandVideo = await fetcher({ parse: 'text' }).get<string>(
    `${videoConverterUrl}/import.php`,
    {
      headers: headers(twoxyaAccount?.cookies ?? ''),
      query: bodyParams,
    },
  )

  try {
    const hrefMatch = resMakeHandVideo.match(
      /<a\b[^>]*href=["']([^"']+)["'][^>]*class=["']vse["'][^>]*>/is,
    )
    const href = hrefMatch ? decodeHtmlEntities(hrefMatch[1]) : ''
    const url = new URL(videoConverterUrl + href)

    const ready = url.searchParams.get('ready')

    if (ready) {
      await c.env._2YXA.put(`id:${ready}`, String(accountIndex), {
        expirationTtl: 60 * 60 * 1, // 1 hour
      })
    }
  } catch {}

  return c.html(
    replaceRelativeHref(resMakeHandVideo, `/relay?u=${encodeURIComponent(videoConverterUrl)}`, {
      smFioLeft15Text: `${accountIndex}`,
    }),
  )
})

router.get('/import/:id', drizzle(), async (c) => {
  const videoId = c.req.param('id')
  if (!validateVideoId(videoId)) {
    throw new HTTPException(400, { message: message.INVALID_VIDEO_ID })
  }

  const formData = createVideoFormData(videoId, webBaseUrl)

  const accountIndex = getIndexByTime()
  const twoxyaAccount = await c
    .get('drizzle')
    .select({
      cookies: schema.twoxyaAccounts.cookies,
    })
    .from(schema.twoxyaAccounts)
    .where(eq(schema.twoxyaAccounts.id, accountIndex))
    .get()

  const resImport = await fetch(`${videoConverterUrl}/import.php`, {
    method: 'POST',
    body: formData,
    headers: headers(twoxyaAccount?.cookies ?? ''),
  })

  const html = await resImport.text()

  return c.html(
    replaceRelativeHref(html, `/relay?u=${encodeURIComponent(videoConverterUrl)}`, {
      smFioLeft15Text: `${accountIndex}`,
    }),
  )
})

router.get('/audio/:id', drizzle(), MainLayout('S60Tube - Audio Detail'), async (c) => {
  const videoId = c.req.param('id')
  if (!validateVideoId(videoId)) {
    throw new HTTPException(400, { message: message.INVALID_VIDEO_ID })
  }

  try {
    const { data: audioResult } = await fetchAudioData({
      drizzle: c.get('drizzle'),
      videoId,
      executionCtx: c.executionCtx,
    })

    if (!audioResult?.data?.link) {
      if (audioResult?.data?.status === 'processing') {
        return c.render(
          <p>
            <b>Audio is being processed...</b>
            <br />
            <b>Title:</b> {audioResult.data.title}
            <br />
            <b>Status:</b> {audioResult.data.status}
            <br />
            <b>Progress:</b> {audioResult.data.progress}%
          </p>,
        )
      }

      throw new HTTPException(404, { message: 'Audio not found' })
    }

    const { link: audioLink } = audioResult.data

    await PlayableLinkService.upsertPlayableLink(c.get('drizzle'), videoId, audioLink, 'audio')

    return c.render(
      <DetailPage
        url={`${webBaseUrl}/audioplayback?v=${videoId}`}
        format={{
          mime_type: 'mp3',
          quality_label: '128Kbps',
        }}
      />,
    )
  } catch (error) {
    return handleHttpError(error, c, {
      context: 'audioPlayback',
      severity: ErrorSeverity.HIGH,
    })
  }
})

export default router
