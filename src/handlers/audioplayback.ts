import { HTTPException } from 'hono/http-exception'

import Innertube, { ClientType, YTNodes } from 'youtubei.js/cf-worker'

import { AccountService } from '../services/account'
import { ApiService } from '../services/api'
import { PlayableLinkService } from '../services/playable-link'
import { fetchWithRetry, forceProxy, proxyUrl } from '../utils'

import type * as schema from '../db/schema'
import type { DrizzleD1Database } from 'drizzle-orm/d1'
import type { Context } from 'hono'

const MAX_RETRIES = 10
const RETRY_DELAY = 2000
const CACHE_VALID_HOURS = 2

const inFlightRequests = new Map<string, Promise<string>>()

async function waitForAudioLink(
  drizzle: DrizzleD1Database<typeof schema>,
  videoId: string,
): Promise<string> {
  const existing = inFlightRequests.get(videoId)
  if (existing) {
    return existing
  }

  const promise = (async () => {
    let retries = 0
    let _lastError: Error

    while (retries < MAX_RETRIES) {
      try {
        return await fetchAudioLinkOnce(drizzle, videoId)
      } catch (error) {
        _lastError = error as Error

        if (retries === MAX_RETRIES - 1) {
          break
        }

        await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY))
        retries++
      }
    }

    throw new HTTPException(404, {
      message: 'Audio not found after retries',
    })
  })()

  inFlightRequests.set(videoId, promise)

  try {
    const result = await promise
    return result
  } finally {
    inFlightRequests.delete(videoId)
  }
}

async function fetchAudioLinkOnce(
  drizzle: DrizzleD1Database<typeof schema>,
  videoId: string,
): Promise<string> {
  const { data: audioResult } = await fetchWithRetry<{
    data: { link: string; status: string; progress: number; title: string }
    apiRemain: number
  }>({
    drizzle,
    fetchFn: async (apiKey: string) => {
      const result = await ApiService.fetchAudioData(videoId, apiKey)
      return result
    },
    updateRemainingFn: async (db, aid, _useApi1, remaining) => {
      await AccountService.updateApiRemaining(db, aid, {
        api3Remain: remaining,
      })
    },
    getAccountFn: AccountService.getAvailableAudioAccount,
  })

  if (!audioResult?.data?.link) {
    throw new HTTPException(404, { message: 'Audio not found' })
  }

  const link = audioResult.data.link.trim()
  if (!link.startsWith('http://') && !link.startsWith('https://')) {
    throw new HTTPException(500, { message: 'Invalid audio link format' })
  }

  return link
}

function createProxyResponse(playableLink: string, headers: Headers): Promise<Response> {
  headers.set('x-url', playableLink)
  return fetch(proxyUrl, { headers })
}

function isValidVideoId(videoId: string): boolean {
  return typeof videoId === 'string' && videoId.length > 0 && videoId.length <= 50
}

function isCacheValid(updatedAt: string | Date): boolean {
  const timestamp = typeof updatedAt === 'string' ? new Date(updatedAt) : updatedAt
  const validHoursAgo = new Date(Date.now() - 1000 * 60 * 60 * CACHE_VALID_HOURS)
  return timestamp > validHoursAgo
}

async function getCachedPlayableLink(
  drizzle: DrizzleD1Database<typeof schema>,
  videoId: string,
): Promise<string | null> {
  try {
    const { link, updatedAt } = await PlayableLinkService.getPlayableLinkWithUpdatedAt(
      drizzle,
      videoId,
      'audio',
    )

    if (isCacheValid(updatedAt)) {
      return link.playableLink
    }
  } catch {}

  return null
}

async function getPlayableLinkWithFallback(
  drizzle: DrizzleD1Database<typeof schema>,
  videoId: string,
  wait: boolean,
  executionCtx: ExecutionContext,
): Promise<string> {
  let playableLink = await getCachedPlayableLink(drizzle, videoId)

  if (!playableLink) {
    if (wait) {
      playableLink = await waitForAudioLink(drizzle, videoId)
    } else {
      playableLink = await fetchAudioLinkOnce(drizzle, videoId)
    }

    executionCtx.waitUntil(
      PlayableLinkService.upsertPlayableLink(drizzle, videoId, playableLink, 'audio'),
    )
  }

  return playableLink
}

async function handleAudioPlayback(
  drizzle: DrizzleD1Database<typeof schema>,
  videoId: string,
  headers: Headers,
  wait: boolean,
  executionCtx: ExecutionContext,
): Promise<Response> {
  if (!isValidVideoId(videoId)) {
    throw new HTTPException(400, { message: 'Invalid video ID format' })
  }

  if (forceProxy.get(`audio:${videoId}`)) {
    const playableLink = await getPlayableLinkWithFallback(drizzle, videoId, wait, executionCtx)
    return createProxyResponse(playableLink, headers)
  }

  const playableLink = await getPlayableLinkWithFallback(drizzle, videoId, wait, executionCtx)
  const res = await fetch(playableLink, { headers })

  if (res.status >= 400) {
    forceProxy.set(`audio:${videoId}`, true)
    return createProxyResponse(playableLink, headers)
  } else {
    forceProxy.delete(`audio:${videoId}`)
    return res
  }
}

export function audioPlaybackHandler(c: Context<AppEnv>) {
  const videoId = c.req.query('v')
  const wait = c.req.query('wait') === '1'

  if (!videoId) {
    throw new HTTPException(400, { message: 'Missing video ID parameter' })
  }

  const headers = new Headers()
  const rangeHeader = c.req.header('Range')
  if (rangeHeader) {
    headers.set('Range', rangeHeader)
  }

  return handleAudioPlayback(c.get('drizzle'), videoId, headers, wait, c.executionCtx)
}

export async function spotifyToYtmusicHandler(c: Context<AppEnv>) {
  const q = decodeURIComponent(c.req.query('q') ?? '')

  if (!q) {
    return c.json({ error: 'Missing query parameter' }, 400)
  }

  const yt = await Innertube.create({
    retrieve_player: false,
    client_type: ClientType.MUSIC,
  })

  const search = await yt.music.search(q, {
    type: 'song',
  })

  const song = search.songs?.contents?.find((item) => item.is(YTNodes.MusicResponsiveListItem))

  if (!song) {
    return c.json({ error: 'No results found' }, 404)
  }

  const { id } = song
  if (!id) {
    throw new HTTPException(500, { message: 'Invalid video ID from search result' })
  }

  const headers = new Headers()
  const rangeHeader = c.req.header('Range')
  if (rangeHeader) {
    headers.set('Range', rangeHeader)
  }

  return handleAudioPlayback(c.get('drizzle'), id, headers, true, c.executionCtx)
}
