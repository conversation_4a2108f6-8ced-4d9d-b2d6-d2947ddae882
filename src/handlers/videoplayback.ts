import { HTTPException } from 'hono/http-exception'

import { PlayableLinkService } from '../services/playable-link'
import { forceProxy, proxyUrl } from '../utils'

import type { Context } from 'hono'

export async function videoPlaybackHandler(c: Context<AppEnv>) {
  const videoId = c.req.query('v')
  const drizzle = c.get('drizzle')
  if (!videoId) {
    throw new HTTPException(400, { message: 'Missing video ID' })
  }

  const { playableLink } = await PlayableLinkService.getPlayableLink(drizzle, videoId, 'video')

  if (!playableLink) {
    throw new HTTPException(404, { message: 'Video not found' })
  }

  const headers = new Headers()
  headers.set('Range', c.req.header('Range') || '')

  if (forceProxy.get(`video:${videoId}`)) {
    forceProxy.set(`video:${videoId}`, true)
    headers.set('x-url', playableLink)

    return fetch(proxyUrl, {
      headers,
    })
  }

  const res = await fetch(playableLink, { headers })

  if (res.status >= 400) {
    forceProxy.set(`video:${videoId}`, true)
    headers.set('x-url', playableLink)

    return fetch(proxyUrl, {
      headers,
    })
  } else {
    forceProxy.delete(`video:${videoId}`)
  }

  return res
}
