/**
 * Cache entry with TTL support
 */
type CacheEntry<V> = {
  /** The cached value */
  value: V
  /** Timestamp when the entry was created */
  timestamp: number
  /** Time to live in milliseconds */
  ttl: number
  /** Access count for LRU tracking */
  accessCount: number
  /** Last access timestamp */
  lastAccess: number
}

/**
 * Configuration options for LRU Cache
 */
export type LRUCacheOptions = {
  /** Maximum number of entries (default: 100) */
  maxSize?: number
  /** Default TTL in milliseconds (default: 1 hour) */
  defaultTTL?: number
  /** Enable automatic cleanup interval (default: true) */
  autoCleanup?: boolean
  /** Cleanup interval in milliseconds (default: 5 minutes) */
  cleanupInterval?: number
}

/**
 * LRU (Least Recently Used) Cache implementation with TTL support.
 *
 * Replaces the unbounded Maps in src/utils/index.ts:35-36 that cause memory leaks.
 * Designed for Cloudflare Workers environment with automatic cleanup and size limits.
 *
 * @template K - Key type
 * @template V - Value type
 *
 * @example
 * ```typescript
 * // Replace: export const pageCache = new Map<string, SearchReturn>()
 * export const pageCache = new LRUCache<string, SearchReturn>({
 *   maxSize: 50,
 *   defaultTTL: 30 * 60 * 1000 // 30 minutes
 * })
 *
 * // Usage
 * pageCache.set('search:query', searchResult)
 * const cached = pageCache.get('search:query')
 * ```
 */
export class LRUCache<K, V> {
  private cache = new Map<K, CacheEntry<V>>()
  private readonly maxSize: number
  private readonly defaultTTL: number
  private readonly autoCleanup: boolean
  private cleanupTimer?: number

  constructor(options: LRUCacheOptions = {}) {
    this.maxSize = options.maxSize ?? 100
    this.defaultTTL = options.defaultTTL ?? 60 * 60 * 1000 // 1 hour
    this.autoCleanup = options.autoCleanup ?? true

    if (this.autoCleanup) {
      const cleanupInterval = options.cleanupInterval ?? 5 * 60 * 1000 // 5 minutes
      this.startAutoCleanup(cleanupInterval)
    }
  }

  /**
   * Retrieves a value from the cache
   *
   * @param key - The key to retrieve
   * @returns The cached value or undefined if not found/expired
   */
  get(key: K): V | undefined {
    const entry = this.cache.get(key)

    if (!entry) {
      return undefined
    }

    // Check if entry has expired
    if (this.isExpired(entry)) {
      this.cache.delete(key)
      return undefined
    }

    // Update access tracking for LRU
    entry.accessCount++
    entry.lastAccess = Date.now()

    return entry.value
  }

  /**
   * Stores a value in the cache
   *
   * @param key - The key to store under
   * @param value - The value to store
   * @param ttl - Optional TTL in milliseconds (uses default if not provided)
   */
  set(key: K, value: V, ttl?: number): void {
    const now = Date.now()
    const entryTTL = ttl ?? this.defaultTTL

    const entry: CacheEntry<V> = {
      value,
      timestamp: now,
      ttl: entryTTL,
      accessCount: 1,
      lastAccess: now,
    }

    // If key already exists, just update it
    if (this.cache.has(key)) {
      this.cache.set(key, entry)
      return
    }

    // Check if we need to evict entries
    if (this.cache.size >= this.maxSize) {
      this.evictLRU()
    }

    this.cache.set(key, entry)
  }

  /**
   * Checks if a key exists in the cache (and is not expired)
   *
   * @param key - The key to check
   * @returns True if key exists and is not expired
   */
  has(key: K): boolean {
    const entry = this.cache.get(key)

    if (!entry) {
      return false
    }

    if (this.isExpired(entry)) {
      this.cache.delete(key)
      return false
    }

    return true
  }

  /**
   * Deletes a key from the cache
   *
   * @param key - The key to delete
   * @returns True if the key existed and was deleted
   */
  delete(key: K): boolean {
    return this.cache.delete(key)
  }

  /**
   * Clears all entries from the cache
   */
  clear(): void {
    this.cache.clear()
  }

  /**
   * Gets the current size of the cache
   *
   * @returns Number of entries in the cache
   */
  get size(): number {
    return this.cache.size
  }

  /**
   * Gets cache statistics for monitoring
   *
   * @returns Object with cache statistics
   */
  getStats(): {
    size: number
    maxSize: number
    hitRate: number
    expiredEntries: number
  } {
    let expiredCount = 0
    let totalAccess = 0

    for (const entry of this.cache.values()) {
      if (this.isExpired(entry)) {
        expiredCount++
      }
      totalAccess += entry.accessCount
    }

    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: totalAccess > 0 ? (this.cache.size - expiredCount) / totalAccess : 0,
      expiredEntries: expiredCount,
    }
  }

  /**
   * Manually triggers cleanup of expired entries
   *
   * @returns Number of entries removed
   */
  cleanup(): number {
    let removedCount = 0
    const now = Date.now()

    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry, now)) {
        this.cache.delete(key)
        removedCount++
      }
    }

    return removedCount
  }

  /**
   * Destroys the cache and stops auto-cleanup
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = undefined
    }
    this.clear()
  }

  /**
   * Checks if a cache entry has expired
   */
  private isExpired(entry: CacheEntry<V>, now: number = Date.now()): boolean {
    return now - entry.timestamp > entry.ttl
  }

  /**
   * Evicts the least recently used entry
   */
  private evictLRU(): void {
    let lruKey: K | undefined
    let lruEntry: CacheEntry<V> | undefined

    for (const [key, entry] of this.cache.entries()) {
      if (!lruEntry || entry.lastAccess < lruEntry.lastAccess) {
        lruKey = key
        lruEntry = entry
      }
    }

    if (lruKey !== undefined) {
      this.cache.delete(lruKey)
    }
  }

  /**
   * Starts automatic cleanup interval
   */
  private startAutoCleanup(interval: number): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, interval) as unknown as number
  }
}

/**
 * Creates a cache instance optimized for page caching
 * Replaces: export const pageCache = new Map<string, SearchReturn>()
 */
export function createPageCache<V>(): LRUCache<string, V> {
  return new LRUCache<string, V>({
    maxSize: 50,
    defaultTTL: 30 * 60 * 1000, // 30 minutes
    autoCleanup: true,
  })
}

/**
 * Creates a cache instance optimized for proxy settings
 * Replaces: export const forceProxy = new Map<string, boolean>()
 */
export function createProxyCache(): LRUCache<string, boolean> {
  return new LRUCache<string, boolean>({
    maxSize: 200,
    defaultTTL: 60 * 60 * 1000, // 1 hour
    autoCleanup: true,
  })
}
