/* eslint-disable no-console */
import { HTTPException } from 'hono/http-exception'

import type { Context } from 'hono'
import type { ContentfulStatusCode } from 'hono/utils/http-status'

/**
 * Standard error response format
 */
export type ErrorResponse = {
  error: string
  message: string
  code?: string
  details?: unknown
  timestamp: string
  context?: string
}

/**
 * Error severity levels for logging
 */
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * Error handling options
 */
export type ErrorHandlerOptions = {
  /** Context information for debugging */
  context?: string
  /** Whether to include error details in response */
  includeDetails?: boolean
  /** Custom error code */
  code?: string
  /** Error severity level */
  severity?: ErrorSeverity
  /** Whether to log the error */
  shouldLog?: boolean
}

/**
 * Centralized error handler that replaces empty catch blocks throughout the codebase.
 *
 * Addresses silent error swallowing found in:
 * - src/handlers/audioplayback.ts:125
 * - src/handlers/shorts-stream.ts:85
 * - src/routes/detail.tsx:277
 * - src/routes/api.ts (multiple locations)
 *
 * @param error - The error to handle (unknown type for safety)
 * @param options - Error handling configuration
 * @returns Standardized error response
 *
 * @example
 * ```typescript
 * // Replace: catch {}
 * // With:
 * catch (error) {
 *   return handleError(error, {
 *     context: 'fetchVideoData',
 *     severity: ErrorSeverity.HIGH
 *   })
 * }
 * ```
 */
export function handleError(error: unknown, options: ErrorHandlerOptions = {}): ErrorResponse {
  const {
    context = 'unknown',
    includeDetails = false,
    code,
    severity = ErrorSeverity.MEDIUM,
    shouldLog = true,
  } = options

  const timestamp = new Date().toISOString()
  let message = 'An unexpected error occurred'
  let statusCode = 500
  let errorCode = code

  // Type guard and error classification
  if (error instanceof HTTPException) {
    const { message: errorMessage, status } = error
    message = errorMessage
    statusCode = status
    errorCode = errorCode || `HTTP_${statusCode}`
  } else if (error instanceof Error) {
    const { message: errorMessage, name } = error
    message = errorMessage
    errorCode = errorCode || name
  } else if (typeof error === 'string') {
    message = error
    errorCode = errorCode || 'STRING_ERROR'
  } else {
    message = 'Unknown error type'
    errorCode = errorCode || 'UNKNOWN_ERROR'
  }

  const errorResponse: ErrorResponse = {
    error: errorCode || 'INTERNAL_ERROR',
    message,
    code: errorCode,
    timestamp,
    context,
  }

  if (includeDetails && error) {
    errorResponse.details = error
  }

  // Log error for debugging (in development/staging)
  if (shouldLog) {
    logError(error, errorResponse, severity)
  }

  return errorResponse
}

/**
 * Creates an HTTP error response for Hono context
 *
 * @param error - The error to handle
 * @param c - Hono context
 * @param options - Error handling options
 * @returns HTTP response with error details
 *
 * @example
 * ```typescript
 * catch (error) {
 *   return handleHttpError(error, c, {
 *     context: 'videoPlayback',
 *     severity: ErrorSeverity.HIGH
 *   })
 * }
 * ```
 */
export function handleHttpError(
  error: unknown,
  c: Context,
  options: ErrorHandlerOptions = {},
): Response {
  const errorResponse = handleError(error, options)

  let statusCode = 500

  if (error instanceof HTTPException) {
    statusCode = error.status
  } else if (isNetworkError(error)) {
    statusCode = 503
  } else if (isValidationError(error)) {
    statusCode = 400
  } else if (isNotFoundError(error)) {
    statusCode = 404
  }

  return c.json(errorResponse, statusCode as ContentfulStatusCode)
}

/**
 * Handles errors with automatic retry logic
 *
 * @param error - The error to handle
 * @param retryCount - Current retry attempt
 * @param maxRetries - Maximum retry attempts
 * @param context - Context information
 * @returns Whether to retry the operation
 *
 * @example
 * ```typescript
 * let retries = 0
 * while (retries < MAX_RETRIES) {
 *   try {
 *     return await riskyOperation()
 *   } catch (error) {
 *     if (!handleRetryableError(error, retries, MAX_RETRIES, 'riskyOperation')) {
 *       throw error
 *     }
 *     retries++
 *   }
 * }
 * ```
 */
export function handleRetryableError(
  error: unknown,
  retryCount: number,
  maxRetries: number,
  context: string,
): boolean {
  const shouldRetry = isRetryableError(error) && retryCount < maxRetries

  handleError(error, {
    context: `${context} (retry ${retryCount}/${maxRetries})`,
    severity: shouldRetry ? ErrorSeverity.LOW : ErrorSeverity.HIGH,
    shouldLog: true,
  })

  return shouldRetry
}

/**
 * Safe error handler for background operations
 * Logs errors without throwing or returning responses
 *
 * @param error - The error to handle
 * @param context - Context information
 *
 * @example
 * ```typescript
 * c.executionCtx.waitUntil(
 *   someBackgroundTask().catch(error =>
 *     handleBackgroundError(error, 'backgroundTask')
 *   )
 * )
 * ```
 */
export function handleBackgroundError(error: unknown, context: string): void {
  handleError(error, {
    context: `background:${context}`,
    severity: ErrorSeverity.MEDIUM,
    shouldLog: true,
  })
}

/**
 * Type guard for HTTP exceptions
 */
export function isHTTPException(error: unknown): error is HTTPException {
  return error instanceof HTTPException
}

/**
 * Type guard for network-related errors
 */
export function isNetworkError(error: unknown): boolean {
  if (error instanceof Error) {
    const message = error.message.toLowerCase()
    return (
      message.includes('network') ||
      message.includes('fetch') ||
      message.includes('timeout') ||
      message.includes('connection')
    )
  }
  return false
}

/**
 * Type guard for validation errors
 */
export function isValidationError(error: unknown): boolean {
  if (error instanceof Error) {
    const message = error.message.toLowerCase()
    return (
      message.includes('validation') ||
      message.includes('invalid') ||
      message.includes('required') ||
      message.includes('format')
    )
  }
  return false
}

/**
 * Type guard for not found errors
 */
export function isNotFoundError(error: unknown): boolean {
  if (error instanceof HTTPException) {
    return error.status === 404
  }
  if (error instanceof Error) {
    const message = error.message.toLowerCase()
    return message.includes('not found') || message.includes('missing')
  }
  return false
}

/**
 * Determines if an error is retryable
 */
export function isRetryableError(error: unknown): boolean {
  if (error instanceof HTTPException) {
    // Retry on server errors and rate limits
    return error.status >= 500 || error.status === 429
  }

  return isNetworkError(error)
}

/**
 * Logs error information for debugging
 */
function logError(error: unknown, errorResponse: ErrorResponse, severity: ErrorSeverity): void {
  const logData = {
    severity,
    error: errorResponse,
    stack: error instanceof Error ? error.stack : undefined,
    timestamp: errorResponse.timestamp,
  }

  // In Cloudflare Workers, use console for logging
  // In production, this could be replaced with structured logging
  switch (severity) {
    case ErrorSeverity.CRITICAL:
    case ErrorSeverity.HIGH:
      console.error('[ERROR]', JSON.stringify(logData, null, 2))
      break
    case ErrorSeverity.MEDIUM:
      console.warn('[WARN]', JSON.stringify(logData, null, 2))
      break
    case ErrorSeverity.LOW:
      console.info('[INFO]', JSON.stringify(logData, null, 2))
      break
  }
}
