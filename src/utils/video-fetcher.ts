import { fetchWithRetry } from './fetch'
import { AccountService } from '../services/account'
import { ApiService } from '../services/api'

import type * as schema from '../db/schema'
import type { VideoResponse } from '../types'
import type { DrizzleD1Database } from 'drizzle-orm/d1'

/**
 * Configuration options for video data fetching
 */
export type VideoFetcherConfig = {
  /** Database instance for account management */
  drizzle: DrizzleD1Database<typeof schema>
  /** Video ID to fetch */
  videoId: string
  /** Geo-location code for API requests */
  cgeo: string
  /** Optional execution context for background tasks */
  executionCtx?: ExecutionContext
}

/**
 * Result of video data fetching operation
 */
export type VideoFetchResult = {
  /** Fetched video data */
  data: VideoResponse
  /** ID of the account used for the request */
  accountId: number
  /** Account details used for the request */
  currentAccount: {
    id: number
    apiKey: string
    api1Remain?: number | null
  }
}

/**
 * Creates a configured video data fetcher that consolidates the duplicate
 * fetchWithRetry patterns found throughout the codebase.
 *
 * This utility eliminates code duplication in:
 * - src/routes/api.ts:286-311
 * - src/routes/detail.tsx:38-59
 * - src/handlers/shorts-stream.ts:108-129
 *
 * @param config - Configuration for the video fetcher
 * @returns Promise resolving to video fetch result
 *
 * @example
 * ```typescript
 * const result = await fetchVideoData({
 *   drizzle: c.get('drizzle'),
 *   videoId: 'dQw4w9WgXcQ',
 *   cgeo: 'US',
 *   executionCtx: c.executionCtx
 * })
 *
 * console.log(result.data.title) // Video title
 * ```
 */
export async function fetchVideoData(config: VideoFetcherConfig): Promise<VideoFetchResult> {
  const { drizzle, videoId, cgeo, executionCtx } = config

  const result = await fetchWithRetry<VideoResponse>({
    drizzle,

    fetchFn: async (apiKey: string, useApi1: boolean) => {
      const response = await ApiService.fetchVideoData(videoId, apiKey, useApi1, cgeo)
      return response
    },

    updateRemainingFn: async (
      db: DrizzleD1Database<typeof schema>,
      accountId: number,
      isApi1: boolean,
      remaining: number,
    ) => {
      await AccountService.updateApiRemaining(
        db,
        accountId,
        isApi1 ? { api1Remain: remaining } : { api2Remain: remaining },
      )
    },

    getAccountFn: async (db: DrizzleD1Database<typeof schema>) => {
      const account = await AccountService.getAvailableAccount(db)
      return {
        id: account.id,
        apiKey: account.apiKey,
        api1Remain: account.api1Remain,
      }
    },
  })

  // Update last used timestamp in background if execution context is available
  if (executionCtx) {
    executionCtx.waitUntil(AccountService.updateLastUsed(drizzle, result.accountId))
  }

  return result
}

/**
 * Creates a configured audio data fetcher for audio-specific requests.
 *
 * @param config - Configuration for the audio fetcher (without cgeo)
 * @returns Promise resolving to audio fetch result
 *
 * @example
 * ```typescript
 * const result = await fetchAudioData({
 *   drizzle: c.get('drizzle'),
 *   videoId: 'dQw4w9WgXcQ',
 *   executionCtx: c.executionCtx
 * })
 * ```
 */
export async function fetchAudioData(config: Omit<VideoFetcherConfig, 'cgeo'>): Promise<{
  data: {
    data: { link: string; status: string; progress: number; title: string }
    apiRemain: number
  }
  accountId: number
  currentAccount: { id: number; apiKey: string }
}> {
  const { drizzle, videoId, executionCtx } = config

  const result = await fetchWithRetry<{
    data: { link: string; status: string; progress: number; title: string }
    apiRemain: number
  }>({
    drizzle,

    fetchFn: async (apiKey: string) => {
      const response = await ApiService.fetchAudioData(videoId, apiKey)
      return response
    },

    updateRemainingFn: async (
      db: DrizzleD1Database<typeof schema>,
      accountId: number,
      _useApi1: boolean,
      remaining: number,
    ) => {
      await AccountService.updateApiRemaining(db, accountId, {
        api3Remain: remaining,
      })
    },

    getAccountFn: AccountService.getAvailableAudioAccount,
  })

  // Update last used timestamp in background if execution context is available
  if (executionCtx) {
    executionCtx.waitUntil(AccountService.updateLastUsed(drizzle, result.accountId))
  }

  return result
}

/**
 * Validates video ID format before making API requests
 *
 * @param videoId - Video ID to validate
 * @returns True if video ID is valid
 */
export function isValidVideoId(videoId: string): boolean {
  return typeof videoId === 'string' && videoId.length > 0 && videoId.length <= 50
}

/**
 * Creates form data for video conversion operations.
 * Consolidates duplicate form data creation found in:
 * - src/routes/detail.tsx:161-167
 * - src/routes/detail.tsx:292-298
 *
 * @param videoId - Video ID for conversion
 * @param webBaseUrl - Base URL for video playback
 * @returns Configured FormData instance
 */
export function createVideoFormData(videoId: string, webBaseUrl: string): FormData {
  const formData = new FormData()
  formData.append('MAX_FILE_SIZE', '*********')
  formData.append('url', `${webBaseUrl}/videoplayback?v=${videoId}`)
  formData.append('ispage', 'no')
  formData.append('name', videoId)
  formData.append('golink', 'Upload')

  return formData
}
