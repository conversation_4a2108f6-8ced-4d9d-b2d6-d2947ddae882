import { proxy } from 'hono/proxy'

import { Platform } from 'youtubei.js/cf-worker'

import { type DataSearch, mapDataSearchToNewFormat } from '../mappers/search.mapper'

import type { SearchReturn } from '../types'

export const message = {
  INVALID_VIDEO_ID: 'Invalid video ID',
  VIDEO_NOT_FOUND: 'Video not found',
}

const VIDEO_ID_REGEX = /^[a-zA-Z0-9_-]{11}$/

export function validateVideoId(id: string): boolean {
  return VIDEO_ID_REGEX.test(id)
}

export function validateKey(key: string): boolean {
  return typeof key === 'string' && key.length > 0
}

export const CONSTANTS = {
  DEFAULT_PAGE: 1,
  ITEMS_PER_PAGE: 10,
  VIDEO_ITAG: 18,
}

export const webBaseUrl = 'http://s60tube.io.vn'
export const proxyUrl = 'http://duyph.io.vn/proxy'
export const videoConverterUrl = 'https://video.2yxa.mobi'
export const API_MIN_THRESHOLD = 5

export const pageCache = new Map<string, SearchReturn>()
export const forceProxy = new Map<string, boolean>()

export const headers = (cookies: string) => ({
  Accept: '*/*',
  'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
  'User-Agent':
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
  'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"macOS"',
  Cookie: `lang=en; ${cookies}`,
})

export const filterData = (data: SearchReturn) => {
  const mappedData = mapDataSearchToNewFormat(data as unknown as DataSearch)

  return mappedData
    .filter((item) => item.videoId && item.lengthSeconds)
    .map((item) => ({
      id: item.videoId,
      thumbnail_overlays: item.thumbnailOverlays,
      title: {
        text: item.title || '',
      },
      short_view_count: {
        text: item.shortViewCountText || '',
      },
      published: {
        text: item.publishedText || '',
      },
      author: {
        id: item.authorId || '',
        name: item.author || '',
      },
      duration: {
        text: item.lengthText || '0:00',
        seconds: item.lengthSeconds || 0,
      },
    }))
}

export const fetchFn = (input: string | Request | URL, init?: RequestInit) => {
  const url = new URL(
    typeof input === 'string' ? input : input instanceof URL ? input.href : input.url,
  )

  if (!url.href.includes('search')) {
    return fetch(input, init)
  }

  const newHeaders = new Headers(init?.headers)

  if (input instanceof Platform.shim.Request) {
    input.headers.forEach((value, key) => newHeaders.set(key, value))
  }

  newHeaders.set('x-url', url.href)

  return proxy(proxyUrl, {
    ...init,
    headers: newHeaders,
    ...(input instanceof Platform.shim.Request ? { raw: input } : {}),
  })
}

export const getCountryFlag = (countryCode: string): string => {
  const codePoints = countryCode
    .toUpperCase()
    .split('')
    .map((char) => 127397 + char.charCodeAt(0))
  return String.fromCodePoint(...codePoints)
}

export function truncateText(text: string, maxLength: number) {
  return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text
}

export const m3uTemplate = (u1: string, u2: string) =>
  `#EXTM3U\n#EXTINF:-1\n${u1}\n#EXTINF:-1\n${u2}`

export * from './datetime'
export * from './dom'
export * from './fetch'
export * from './pagination'
