{"name": "s60tube", "scripts": {"cf-typegen": "wrangler types --env-interface CloudflareBindings", "d1:backup": "wrangler d1 export DB --remote --output backup.sql", "d1:migration:apply": "wrangler d1 migrations apply DB", "deploy": "wrangler deploy --minify", "dev": "wrangler dev", "format": "prettier --write src/**/*.{ts,tsx} views/*.tsx", "lint": "eslint --fix src/**/*.{ts,tsx} views/*.tsx --quiet"}, "dependencies": {"dom-serializer": "^2.0.0", "domhandler": "^5.0.3", "drizzle-orm": "^0.44.4", "hono": "^4.8.10", "htmlparser2": "^10.0.0", "itty-fetcher": "^1.0.10", "youtubei.js": "^15.0.1"}, "devDependencies": {"@eslint/js": "^9.32.0", "@types/domhandler": "^3.1.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "drizzle-kit": "^0.31.4", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import-x": "^4.16.1", "eslint-plugin-simple-import-sort": "^12.1.1", "jiti": "^2.5.1", "prettier": "^3.6.2", "prettier-plugin-organize-imports": "^4.2.0", "typescript": "^5.8.3", "wrangler": "^4.26.1"}, "packageManager": "pnpm@10.13.1"}